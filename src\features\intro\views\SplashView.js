class SplashView {
  constructor() {
    this.container = null;
    this.progressBar = null;
    this.loadingText = null;
  }

  render(data) {
    console.log('🎬 SplashView - Rendering splash screen with data:', data);
    this.container = document.createElement('div');
    this.container.className = 'splash-container';

    this.container.innerHTML = `
      <div style="color: white; text-align: center; font-size: 2rem;">
        <h1>SPLASH SCREEN TESTING</h1>
        <p>${data.brandName}</p>
        <p>Loading...</p>
      </div>
    `;

    this.progressBar = this.container.querySelector('.progress-fill');
    this.loadingText = this.container.querySelector('.loading-text');

    return this.container;
  }

  updateProgress(percentage) {
    if (this.progressBar) {
      this.progressBar.style.width = `${percentage}%`;
    }
  }

  updateLoadingText(text) {
    if (this.loadingText) {
      this.loadingText.textContent = text;
    }
  }

  startLoadingAnimation() {
    // Add loading animation class
    if (this.container) {
      this.container.classList.add('loading');
    }
  }

  fadeOut() {
    return new Promise((resolve) => {
      if (this.container) {
        this.container.classList.add('fade-out');
        setTimeout(resolve, 500); // Match CSS transition duration
      } else {
        resolve();
      }
    });
  }

  mount(container) {
    console.log('🎬 SplashView - Mounting splash to container:', container);
    if (this.container) {
      container.appendChild(this.container);
      console.log('🎬 SplashView - Splash container appended, triggering animation');
      // Trigger entrance animation
      setTimeout(() => {
        this.container.classList.add('visible');
        console.log('🎬 SplashView - Visible class added');
      }, 100);
    }
  }

  unmount() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }

  destroy() {
    this.unmount();
    this.container = null;
    this.progressBar = null;
    this.loadingText = null;
  }
}

export default SplashView;
