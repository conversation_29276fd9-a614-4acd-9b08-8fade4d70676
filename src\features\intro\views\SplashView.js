class SplashView {
  constructor() {
    this.container = null;
    this.progressBar = null;
    this.loadingText = null;
  }

  render(data) {
    this.container = document.createElement('div');
    this.container.className = 'splash-container';

    this.container.innerHTML = `
      <div class="splash-content">
        <div class="splash-logo">
          <div class="logo-icon">
            <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9M21 11V13L15 12.5V11M21 15V17L15 16.5V15M9 9H5C3.9 9 3 9.9 3 11V13C3 14.1 3.9 15 5 15H9C10.1 15 11 14.1 11 13V11C11 9.9 10.1 9 9 9ZM12 17.5C12 19.43 10.43 21 8.5 21S5 19.43 5 17.5 6.57 14 8.5 14 12 15.57 12 17.5Z" fill="currentColor"/>
            </svg>
          </div>
          <h1 class="brand-name">${data.brandName}</h1>
          <p class="tagline">${data.tagline}</p>
        </div>

        <div class="splash-description">
          <p>${data.description}</p>
        </div>

        <div class="loading-section">
          <div class="loading-text">${data.loadingText}</div>
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="splash-background">
        <div class="background-text">AUREAVOICE</div>
      </div>
    `;

    this.progressBar = this.container.querySelector('.progress-fill');
    this.loadingText = this.container.querySelector('.loading-text');

    return this.container;
  }

  updateProgress(percentage) {
    if (this.progressBar) {
      this.progressBar.style.width = `${percentage}%`;
    }
  }

  updateLoadingText(text) {
    if (this.loadingText) {
      this.loadingText.textContent = text;
    }
  }

  startLoadingAnimation() {
    // Add loading animation class
    if (this.container) {
      this.container.classList.add('loading');
    }
  }

  fadeOut() {
    return new Promise((resolve) => {
      if (this.container) {
        this.container.classList.add('fade-out');
        setTimeout(resolve, 500); // Match CSS transition duration
      } else {
        resolve();
      }
    });
  }

  mount(container) {
    if (this.container) {
      container.appendChild(this.container);
      // Trigger entrance animation
      setTimeout(() => {
        this.container.classList.add('visible');
      }, 100);
    }
  }

  unmount() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }

  destroy() {
    this.unmount();
    this.container = null;
    this.progressBar = null;
    this.loadingText = null;
  }
}

export default SplashView;
