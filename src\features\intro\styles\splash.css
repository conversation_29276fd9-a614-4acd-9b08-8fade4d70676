/* Splash Screen Styles */

.splash-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  overflow: hidden;
}

.splash-container.visible {
  opacity: 1;
}

.splash-container.fade-out {
  opacity: 0;
}

.splash-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
  max-width: 600px;
  padding: 2rem;
}

.splash-logo {
  margin-bottom: 3rem;
}

.logo-icon {
  display: inline-block;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  animation: logoFloat 3s ease-in-out infinite;
}

.brand-name {
  font-size: 3.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out 0.3s both;
}

.tagline {
  font-size: 1.2rem;
  font-weight: 400;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.splash-description {
  margin-bottom: 3rem;
  animation: fadeInUp 1s ease-out 0.9s both;
}

.splash-description p {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.8;
  margin: 0;
}

.loading-section {
  animation: fadeInUp 1s ease-out 1.2s both;
}

.loading-text {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  font-weight: 500;
}

.progress-container {
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffffff, #f0f0f0);
  border-radius: 2px;
  width: 0%;
  transition: width 0.3s ease-out;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.splash-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.background-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12rem;
  font-weight: 900;
  color: rgba(255, 255, 255, 0.05);
  pointer-events: none;
  user-select: none;
  white-space: nowrap;
  letter-spacing: 0.1em;
  animation: backgroundPulse 4s ease-in-out infinite;
}

/* Animations */
@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backgroundPulse {
  0%, 100% {
    opacity: 0.05;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.08;
    transform: translate(-50%, -50%) scale(1.02);
  }
}

/* Loading animation */
.splash-container.loading .progress-fill {
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% {
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .splash-content {
    padding: 1.5rem;
  }

  .brand-name {
    font-size: 2.5rem;
  }

  .tagline {
    font-size: 1rem;
  }

  .splash-description p {
    font-size: 1rem;
  }

  .background-text {
    font-size: 8rem;
  }

  .logo-icon {
    width: 60px;
    height: 60px;
  }

  .logo-icon svg {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .brand-name {
    font-size: 2rem;
  }

  .tagline {
    font-size: 0.9rem;
  }

  .splash-description p {
    font-size: 0.9rem;
  }

  .background-text {
    font-size: 5rem;
  }

  .progress-container {
    max-width: 250px;
  }
}
