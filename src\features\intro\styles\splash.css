/* Professional Splash Screen Styles */

.splash-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 99999 !important;
  opacity: 0;
  transition: opacity 0.6s ease-in-out;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.splash-container.visible {
  opacity: 1;
}

.splash-container.fade-out {
  opacity: 0;
}

.splash-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
  max-width: 800px;
  padding: 2rem;
}

.brand-container {
  margin-bottom: 4rem;
}

.brand-text {
  font-size: 4.5rem;
  font-weight: 300;
  margin: 0 0 1rem 0;
  letter-spacing: 0.15em;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1.2s ease-out 0.3s both;
  line-height: 1.1;
}

.brand-aurea {
  color: #ffffff;
  font-weight: 300;
}

.brand-voice {
  color: #fbbf24;
  font-weight: 400;
  display: inline-block;
  position: relative;
  transition: transform 0.1s ease-out, text-shadow 0.3s ease-out;
}

.tagline {
  font-size: 1.1rem;
  font-weight: 300;
  margin: 0 0 2rem 0;
  opacity: 0.85;
  letter-spacing: 0.05em;
  animation: fadeInUp 1s ease-out 0.8s both;
  color: #e5e7eb;
}

.loading-section {
  animation: fadeInUp 1s ease-out 1.4s both;
}

.progress-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.progress-bar {
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 1px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  border-radius: 1px;
  width: 0%;
  transition: width 0.8s ease-out;
  box-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
}

/* Jackpot effect now handled by JavaScript */

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* No loading animation - static progress bar */

/* Responsive Design */
@media (max-width: 768px) {
  .splash-content {
    padding: 1.5rem;
    max-width: 90%;
  }

  .brand-text {
    font-size: 3.2rem;
    letter-spacing: 0.12em;
  }

  .tagline {
    font-size: 1rem;
  }

  .progress-container {
    max-width: 320px;
  }
}

@media (max-width: 480px) {
  .brand-text {
    font-size: 2.5rem;
    letter-spacing: 0.1em;
  }

  .tagline {
    font-size: 0.9rem;
  }

  .progress-container {
    max-width: 280px;
  }

  .splash-content {
    padding: 1rem;
  }
}
