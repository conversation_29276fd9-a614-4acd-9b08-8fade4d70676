import Router from './utils/router.js';
import {
  WelcomePresenter,
  TestPresenter,
  ResultPresenter,
  SplashPresenter,
  IntroModel,
  SplashModel,
  DashboardPresenter,
  DashboardModel
} from './features/index.js';
import RecordingManager from './utils/RecordingManager.js';
import { FooterPresenter } from './shared/index.js';
import './utils/ViewTransitionHelper.js'; // Initialize View Transition API support

class App {
  constructor() {
    this.router = new Router();
    this.introModel = new IntroModel();
    this.splashModel = new SplashModel();
    this.dashboardModel = new DashboardModel();
    this.currentPresenter = null;
    this.footer = new FooterPresenter();

    this.setupRoutes();
    this.setupGlobalCleanup();
    this.initializeFooter();
  }

  setupRoutes() {
    this.router.addRoute('/', () => this.showWelcome());
    this.router.addRoute('/test', () => this.showTest());
    this.router.addRoute('/result', (resultData) => this.showResult(resultData));
    this.router.addRoute('/dashboard', () => this.showDashboard());
    this.router.addRoute('/splash', () => this.showSplash());
  }

  showWelcome() {
    this.destroyCurrentPresenter();
    this.currentPresenter = new WelcomePresenter(this.introModel);
    this.currentPresenter.init();
    this.introModel.setCurrentPage('welcome');
  }

  showTest() {
    this.destroyCurrentPresenter();
    this.currentPresenter = new TestPresenter(this.introModel);
    this.currentPresenter.init();
    this.introModel.setCurrentPage('test');
  }

  showResult(resultData = null) {
    this.destroyCurrentPresenter();
    this.currentPresenter = new ResultPresenter(resultData, this.introModel);
    this.currentPresenter.init();
    this.introModel.setCurrentPage('result');
  }

  showDashboard() {
    // Check if splash screen should be shown
    const shouldShow = this.splashModel.shouldShowSplash();

    if (shouldShow) {
      this.showSplash();
    } else {
      this.showDashboardDirect();
    }
  }

  showSplash() {
    this.destroyCurrentPresenter();
    this.currentPresenter = new SplashPresenter(this.splashModel, () => {
      this.showDashboardDirect();
    });
    this.currentPresenter.init();
  }

  showDashboardDirect() {
    this.destroyCurrentPresenter();
    this.currentPresenter = new DashboardPresenter(this.dashboardModel);
    this.currentPresenter.init();
    this.dashboardModel.setCurrentPage('dashboard');
  }

  setupGlobalCleanup() {
    window.addEventListener('beforeunload', () => {
      RecordingManager.forceStop();
    });

    window.addEventListener('pagehide', () => {
      RecordingManager.forceStop();
    });

    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        RecordingManager.forceStop();
      }
    });
  }

  destroyCurrentPresenter() {
    if (this.currentPresenter) {
      this.currentPresenter.destroy();
      this.currentPresenter = null;
    }
  }

  initializeFooter() {
    // Mount footer to body, it will be persistent across all pages
    this.footer.mount(document.body);
  }

  // Method to reset splash state (useful for testing)
  resetSplashState() {
    this.splashModel.resetSplashState();
  }

  // Method to force show splash (for testing)
  forceShowSplash() {
    this.showSplash();
  }
}

document.addEventListener('DOMContentLoaded', () => {
  const app = new App();

  // Make app available globally for testing/debugging
  window.aureaVoiceApp = app;

  // Add global helper for testing
  window.clearSplashState = () => {
    sessionStorage.removeItem('aureavoice_splash_shown');
    console.log('🎬 Splash state cleared');
  };
});
