class SplashModel {
  constructor() {
    this.brandName = 'AureaVoice';
    this.tagline = 'Master Your American Accent';
    this.description = 'Platform pembelajaran aksen Amerika berbasis AI untuk penutur bahasa Indonesia';
    this.loadingText = 'Mempersiapkan dashboard...';
    this.minimumDisplayTime = 2000; // 2 seconds minimum display
    this.hasShownSplash = false;
  }

  getBrandName() {
    return this.brandName;
  }

  getTagline() {
    return this.tagline;
  }

  getDescription() {
    return this.description;
  }

  getLoadingText() {
    return this.loadingText;
  }

  getMinimumDisplayTime() {
    return this.minimumDisplayTime;
  }

  setHasShownSplash(shown) {
    this.hasShownSplash = shown;
    // Store in sessionStorage so it persists during the session
    sessionStorage.setItem('aureavoice_splash_shown', shown.toString());
  }

  getHasShownSplash() {
    // Check sessionStorage first
    const stored = sessionStorage.getItem('aureavoice_splash_shown');
    if (stored !== null) {
      this.hasShownSplash = stored === 'true';
    }
    return this.hasShownSplash;
  }

  shouldShowSplash() {
    return !this.getHasShownSplash();
  }

  resetSplashState() {
    this.hasShownSplash = false;
    sessionStorage.removeItem('aureavoice_splash_shown');
  }
}

export default SplashModel;
