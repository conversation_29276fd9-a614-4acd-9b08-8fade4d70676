import SplashView from '../views/SplashView.js';

class SplashPresenter {
  constructor(model, onComplete) {
    this.model = model;
    this.view = null;
    this.onComplete = onComplete;
    this.startTime = null;
    this.progressInterval = null;
  }

  init() {
    // Remove dashboard mode class temporarily
    this.originalBodyClasses = document.body.className;
    document.body.classList.remove('dashboard-mode');

    // Hide footer during splash
    this.hideFooter();

    const splashData = this.getSplashData();
    this.view = new SplashView();
    this.render(splashData);
    this.startLoadingSequence();
  }

  getSplashData() {
    return {
      brandName: this.model.getBrandName(),
      tagline: this.model.getTagline(),
      description: this.model.getDescription()
    };
  }

  render(data) {
    const appElement = document.getElementById('app');
    appElement.innerHTML = '';

    const viewElement = this.view.render(data);
    this.view.mount(appElement);
  }

  startLoadingSequence() {
    this.startTime = Date.now();
    this.view.startLoadingAnimation();

    // Start jackpot effect after 1.5 seconds
    setTimeout(() => {
      this.view.startJackpotEffect();
    }, 1500);

    // Simulate loading progress
    this.simulateProgress();
  }

  simulateProgress() {
    const loadingSteps = [
      { progress: 20, delay: 800 },
      { progress: 45, delay: 1000 },
      { progress: 70, delay: 1200 },
      { progress: 90, delay: 800 },
      { progress: 100, delay: 500 }
    ];

    let currentStep = 0;

    const executeStep = () => {
      if (currentStep < loadingSteps.length) {
        const step = loadingSteps[currentStep];

        this.view.updateProgress(step.progress);

        currentStep++;
        setTimeout(executeStep, step.delay);
      } else {
        this.completeLoading();
      }
    };

    // Start the loading sequence
    setTimeout(executeStep, 500);
  }

  completeLoading() {
    const elapsedTime = Date.now() - this.startTime;
    const minimumTime = this.model.getMinimumDisplayTime();
    const remainingTime = Math.max(0, minimumTime - elapsedTime);

    // Ensure minimum display time
    setTimeout(() => {
      this.finishSplash();
    }, remainingTime);
  }

  async finishSplash() {
    // Mark splash as shown
    this.model.setHasShownSplash(true);

    // Fade out animation
    await this.view.fadeOut();

    // Restore original body classes
    if (this.originalBodyClasses) {
      document.body.className = this.originalBodyClasses;
    }

    // Show footer again
    this.showFooter();

    // Call completion callback
    if (this.onComplete) {
      this.onComplete();
    }
  }

  hideFooter() {
    const footer = document.getElementById('footer');
    if (footer) {
      footer.style.display = 'none';
    }
  }

  showFooter() {
    const footer = document.getElementById('footer');
    if (footer) {
      footer.style.display = 'flex';
    }
  }

  destroy() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }

    // Restore original body classes
    if (this.originalBodyClasses) {
      document.body.className = this.originalBodyClasses;
    }

    // Ensure footer is shown when splash is destroyed
    this.showFooter();

    if (this.view) {
      this.view.destroy();
      this.view = null;
    }
  }
}

export default SplashPresenter;
