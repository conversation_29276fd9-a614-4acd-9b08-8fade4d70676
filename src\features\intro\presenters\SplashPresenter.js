import SplashView from '../views/SplashView.js';

class SplashPresenter {
  constructor(model, onComplete) {
    this.model = model;
    this.view = null;
    this.onComplete = onComplete;
    this.startTime = null;
    this.progressInterval = null;
  }

  init() {
    console.log('🎬 SplashPresenter - Initializing splash screen');

    // Remove dashboard mode class temporarily
    this.originalBodyClasses = document.body.className;
    document.body.classList.remove('dashboard-mode');

    // Hide footer during splash
    this.hideFooter();

    const splashData = this.getSplashData();
    this.view = new SplashView();
    this.render(splashData);
    this.startLoadingSequence();
  }

  getSplashData() {
    return {
      brandName: this.model.getBrandName(),
      tagline: this.model.getTagline(),
      description: this.model.getDescription(),
      loadingText: this.model.getLoadingText()
    };
  }

  render(data) {
    const appElement = document.getElementById('app');
    appElement.innerHTML = '';

    const viewElement = this.view.render(data);
    this.view.mount(appElement);
  }

  startLoadingSequence() {
    this.startTime = Date.now();
    this.view.startLoadingAnimation();
    
    // Simulate loading progress
    this.simulateProgress();
  }

  simulateProgress() {
    let progress = 0;
    const loadingSteps = [
      { progress: 20, text: 'Memuat komponen dashboard...', delay: 300 },
      { progress: 45, text: 'Menyiapkan data pengguna...', delay: 500 },
      { progress: 70, text: 'Mengonfigurasi grafik...', delay: 400 },
      { progress: 90, text: 'Menyelesaikan persiapan...', delay: 300 },
      { progress: 100, text: 'Siap!', delay: 200 }
    ];

    let currentStep = 0;

    const executeStep = () => {
      if (currentStep < loadingSteps.length) {
        const step = loadingSteps[currentStep];
        
        this.view.updateProgress(step.progress);
        this.view.updateLoadingText(step.text);
        
        currentStep++;
        setTimeout(executeStep, step.delay);
      } else {
        this.completeLoading();
      }
    };

    // Start the loading sequence
    setTimeout(executeStep, 200);
  }

  completeLoading() {
    const elapsedTime = Date.now() - this.startTime;
    const minimumTime = this.model.getMinimumDisplayTime();
    const remainingTime = Math.max(0, minimumTime - elapsedTime);

    // Ensure minimum display time
    setTimeout(() => {
      this.finishSplash();
    }, remainingTime);
  }

  async finishSplash() {
    // Mark splash as shown
    this.model.setHasShownSplash(true);

    // Fade out animation
    await this.view.fadeOut();

    // Restore original body classes
    if (this.originalBodyClasses) {
      document.body.className = this.originalBodyClasses;
    }

    // Show footer again
    this.showFooter();

    // Call completion callback
    if (this.onComplete) {
      this.onComplete();
    }
  }

  hideFooter() {
    const footer = document.getElementById('footer');
    if (footer) {
      footer.style.display = 'none';
    }
  }

  showFooter() {
    const footer = document.getElementById('footer');
    if (footer) {
      footer.style.display = 'flex';
    }
  }

  destroy() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }

    // Restore original body classes
    if (this.originalBodyClasses) {
      document.body.className = this.originalBodyClasses;
    }

    // Ensure footer is shown when splash is destroyed
    this.showFooter();

    if (this.view) {
      this.view.destroy();
      this.view = null;
    }
  }
}

export default SplashPresenter;
