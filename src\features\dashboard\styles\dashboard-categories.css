/* Dashboard Categories Styles */

/* Category Grid */
.categories-section {
  margin-top: 3rem;
}

.categories-title {
  font-size: 3rem;
  font-weight: 700;
  color: #1e293b; /* slate-800 */
  margin-bottom: 2rem;
  text-align: center;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 3rem;
  width: 100%;
}

.category-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  padding: 2rem 1.5rem;
  text-decoration: none;
  color: inherit;
  border: 1px solid #e2e8f0; /* slate-200 */
  transition: all 0.2s;
  display: block;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
  border-color: #0079FF;
}

.category-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.category-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1e293b; /* slate-800 */
  margin-bottom: 0.75rem;
}

.category-description {
  color: #64748b; /* slate-600 */
  line-height: 1.5;
  margin: 0;
  font-size: 1.25rem;
}

/* Category progress indicators */
.category-progress {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.category-progress-bar {
  width: 100%;
  height: 6px;
  background-color: #f1f5f9;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.category-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0079FF 0%, #004AAD 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.category-progress-text {
  font-size: 0.875rem;
  color: #64748b;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-progress-percentage {
  font-weight: 600;
  color: #0079FF;
}

/* Responsive categories */
@media (max-width: 1024px) {
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
    width: 100%;
  }
}

@media (max-width: 768px) {
  .categories-section {
    margin-top: 2rem;
  }

  .categories-title {
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 1rem;
    max-width: none;
  }

  .category-card {
    padding: 1.5rem 1rem;
  }

  .category-icon {
    font-size: 2rem;
  }

  .category-title {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .categories-title {
    font-size: 1.5rem;
  }
  
  .category-card {
    padding: 1rem 0.75rem;
  }
  
  .category-icon {
    font-size: 1.75rem;
  }
  
  .category-title {
    font-size: 1rem;
  }
  
  .category-description {
    font-size: 0.875rem;
  }
}
